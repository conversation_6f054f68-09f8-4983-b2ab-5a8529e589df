# 手动精修功能测试指南 - 更新版

## 问题修复说明

本次更新解决了以下问题：
1. ✅ 首次添加的人像蒙层已放入历史记录，重置时恢复到首次状态
2. ✅ 修复重置后的死循环问题，优化历史记录管理
3. ✅ 拖动功能选中时显示原生小手光标（grab）
4. ✅ 优化画笔羽化效果，减少锯齿问题
5. ✅ 只在切换模式时调用 updateMattingImageFromCanvas，避免频繁更新

## 测试环境准备

1. 确保项目已成功构建（`npm run build` 通过）
2. 启动开发服务器：`npm run dev`
3. 准备测试图片：
   - 包含人像的图片
   - 背景有红色元素的图片（用于测试问题一的解决）
   - 不同尺寸的图片

## 功能测试步骤

### 1. 基础功能测试

#### 1.1 进入手动精修模式
- [ ] 上传一张包含人像的图片
- [ ] 等待自动抠图完成
- [ ] 点击"手动精修"选项卡
- [ ] 验证：应该看到双画布结构，显示原图和红色蒙层

#### 1.2 画布显示测试
- [ ] 验证显示画布显示原图作为背景
- [ ] 验证红色蒙层正确覆盖人像区域
- [ ] 验证操作画布在上层，支持鼠标交互
- [ ] 切换到"原图"模式，验证显示正确

### 2. 画笔操作测试

#### 2.1 保留模式画笔
- [ ] 选择"保留"画笔
- [ ] 调整画笔大小（测试不同尺寸）
- [ ] 在非人像区域绘制
- [ ] 验证：绘制区域应显示红色蒙层
- [ ] 验证：边缘应有羽化效果，无明显锯齿

#### 2.2 擦除模式画笔
- [ ] 选择"擦除"画笔
- [ ] 在人像区域绘制
- [ ] 验证：绘制区域的红色蒙层应被移除
- [ ] 验证：只擦除红色蒙层，不影响原图显示

#### 2.3 边缘柔和度测试
- [ ] 调整边缘柔和度参数
- [ ] 进行画笔操作
- [ ] 验证：柔和度越高，边缘越模糊

### 3. 拖拽和缩放测试

#### 3.1 拖拽功能
- [ ] 点击拖拽按钮进入拖拽模式
- [ ] 验证：鼠标光标变为小手样式（grab）
- [ ] 验证：画笔操作被禁用
- [ ] 拖拽画布容器
- [ ] 验证：两个画布同步移动
- [ ] 退出拖拽模式，验证画笔功能恢复
- [ ] 验证：鼠标光标恢复为画笔样式

#### 3.2 缩放功能
- [ ] 使用缩放按钮放大/缩小
- [ ] 验证：两个画布同步缩放
- [ ] 在缩放状态下进行画笔操作
- [ ] 验证：画笔操作精度正确

### 4. 历史记录测试

#### 4.1 初始状态历史记录
- [ ] 进入手动精修模式
- [ ] 验证：初始人像蒙层已自动保存到历史记录
- [ ] 进行画笔操作后重置
- [ ] 验证：重置后恢复到初始人像蒙层状态

#### 4.2 撤销功能
- [ ] 进行多次画笔操作
- [ ] 点击"撤销"按钮
- [ ] 验证：操作被正确撤销
- [ ] 连续撤销多次操作
- [ ] 验证：不会出现死循环或无限日志

#### 4.3 恢复功能
- [ ] 在撤销后点击"恢复"按钮
- [ ] 验证：操作被正确恢复

#### 4.4 重置功能
- [ ] 进行多次画笔操作
- [ ] 点击"重置"按钮
- [ ] 验证：画布恢复到初始状态（只有原始人像蒙层）
- [ ] 验证：不会出现死循环或控制台错误

### 5. 抠图生成测试

#### 5.1 模式切换更新测试
- [ ] 在手动精修模式进行画笔操作
- [ ] 验证：画笔操作时不会立即更新抠图（性能优化）
- [ ] 切换到"换背景"模式
- [ ] 验证：切换时自动更新抠图结果，反映画笔修改
- [ ] 切换到"改尺寸"模式
- [ ] 验证：抠图结果正确更新
- [ ] 切换到"原图"查看
- [ ] 验证：抠图结果正确更新

#### 5.2 边缘质量测试
- [ ] 在人像边缘进行精细修改
- [ ] 切换模式查看最终抠图效果
- [ ] 验证：边缘平滑，锯齿明显减少
- [ ] 验证：羽化效果自然，过渡平滑

### 6. 问题验证测试

#### 6.1 问题一：红色背景干扰
- [ ] 使用背景包含红色的图片
- [ ] 进入手动精修模式
- [ ] 验证：红色背景不会被误识别为蒙层
- [ ] 进行画笔操作
- [ ] 验证：只有画笔绘制的红色蒙层被识别

#### 6.2 问题二：画笔边缘锯齿
- [ ] 使用较大的画笔进行操作
- [ ] 查看画笔轨迹边缘
- [ ] 验证：边缘有羽化效果
- [ ] 验证：无明显锯齿

#### 6.3 问题三：生成抠图边缘锯齿
- [ ] 完成手动精修
- [ ] 查看最终生成的抠图
- [ ] 验证：抠图边缘有羽化处理
- [ ] 验证：边缘平滑自然

## 性能测试

### 7.1 响应速度测试
- [ ] 快速连续画笔操作
- [ ] 验证：操作响应及时，无明显延迟
- [ ] 验证：不会出现卡顿现象

### 7.2 内存使用测试
- [ ] 长时间使用手动精修功能
- [ ] 监控浏览器内存使用
- [ ] 验证：无明显内存泄漏

## 兼容性测试

### 8.1 浏览器兼容性
- [ ] Chrome 浏览器测试
- [ ] Firefox 浏览器测试
- [ ] Safari 浏览器测试（如适用）

### 8.2 设备兼容性
- [ ] 桌面设备测试
- [ ] 平板设备测试（如适用）

## 预期结果

所有测试项目都应该通过，特别是：

1. **双画布架构正常工作**：显示画布和操作画布各司其职
2. **画笔操作流畅**：保留/擦除功能正确，边缘有羽化效果
3. **拖拽缩放正常**：两个画布同步操作
4. **历史记录完整**：撤销/恢复/重置功能正确
5. **抠图质量提升**：边缘平滑，无锯齿，羽化效果自然
6. **红色干扰解决**：背景红色不影响蒙层识别

如果发现任何问题，请记录具体的复现步骤和预期与实际结果的差异。
